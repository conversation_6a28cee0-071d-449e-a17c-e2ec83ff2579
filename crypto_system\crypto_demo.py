"""
Crypto Manipulation Detection System Demo
Demonstrates the adapted system working with simulated Binance data
"""
import asyncio
import numpy as np
from datetime import datetime, timedelta
from typing import List
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Mock data structures for demo (since we don't have actual Binance connection)
class MockCryptoMarketData:
    def __init__(self, symbol: str, price: float, volume: float, bid_qty: float, ask_qty: float, timestamp: datetime = None):
        self.symbol = symbol
        self.price = price
        self.volume = volume
        self.bid_qty = bid_qty
        self.ask_qty = ask_qty
        self.bid_price = price * 0.999  # 0.1% spread
        self.ask_price = price * 1.001
        self.timestamp = timestamp or datetime.now()

        # Additional crypto fields
        self.price_change_24h = np.random.uniform(-5, 5)
        self.volume_24h = volume * 24
        self.high_24h = price * 1.05
        self.low_24h = price * 0.95
        self.trades_count = int(volume / 100)

def generate_crypto_spoofing_scenario(symbol: str, base_price: float) -> List[MockCryptoMarketData]:
    """
    Generate simulated crypto data with spoofing pattern
    """
    data = []
    current_time = datetime.now()

    # Normal trading for 30 seconds
    for i in range(30):
        price = base_price + np.random.normal(0, base_price * 0.001)  # 0.1% volatility
        volume = np.random.uniform(1000, 5000)  # Normal volume
        bid_qty = np.random.uniform(10, 50)
        ask_qty = np.random.uniform(10, 50)

        data.append(MockCryptoMarketData(
            symbol=symbol,
            price=price,
            volume=volume,
            bid_qty=bid_qty,
            ask_qty=ask_qty,
            timestamp=current_time + timedelta(seconds=i)
        ))

    # SPOOFING PATTERN: Large bid spike followed by quick removal
    spoof_time = current_time + timedelta(seconds=30)

    # 1. Large bid appears (spoofing)
    large_bid_qty = 500  # 10x normal size
    data.append(MockCryptoMarketData(
        symbol=symbol,
        price=base_price,
        volume=2000,
        bid_qty=large_bid_qty,  # LARGE BID
        ask_qty=20,
        timestamp=spoof_time
    ))

    # 2. Price barely moves despite large bid (suspicious)
    data.append(MockCryptoMarketData(
        symbol=symbol,
        price=base_price + 0.001,  # Minimal price impact
        volume=3000,
        bid_qty=large_bid_qty,
        ask_qty=25,
        timestamp=spoof_time + timedelta(seconds=2)
    ))

    # 3. Large bid suddenly disappears (spoofing confirmed)
    data.append(MockCryptoMarketData(
        symbol=symbol,
        price=base_price - 0.002,  # Price drops when support removed
        volume=1500,
        bid_qty=15,  # BACK TO NORMAL
        ask_qty=20,
        timestamp=spoof_time + timedelta(seconds=5)
    ))

    # Continue normal trading
    for i in range(10):
        price = base_price + np.random.normal(0, base_price * 0.001)
        volume = np.random.uniform(1000, 3000)
        bid_qty = np.random.uniform(10, 30)
        ask_qty = np.random.uniform(10, 30)

        data.append(MockCryptoMarketData(
            symbol=symbol,
            price=price,
            volume=volume,
            bid_qty=bid_qty,
            ask_qty=ask_qty,
            timestamp=spoof_time + timedelta(seconds=10 + i)
        ))

    return data

async def demo_crypto_detection():
    """
    Demonstrate crypto manipulation detection
    """
    print("🚀 Crypto Manipulation Detection System Demo")
    print("=" * 60)

    try:
        # Import the crypto detector (with error handling for missing dependencies)
        try:
            from detection.crypto_spoofing_detector import crypto_spoofing_detector
            print("✅ Crypto spoofing detector loaded successfully")
        except ImportError as e:
            print(f"❌ Could not import crypto detector: {e}")
            print("📝 Note: This demo requires numpy and numba packages")
            return

        # Generate test data for multiple crypto pairs
        crypto_pairs = [
            ("BTCUSDT", 45000.0),
            ("ETHUSDT", 3000.0),
            ("BNBUSDT", 300.0)
        ]

        all_signals = []

        for symbol, base_price in crypto_pairs:
            print(f"\n📊 Analyzing {symbol} (${base_price:,.0f})")
            print("-" * 40)

            # Generate spoofing scenario
            market_data = generate_crypto_spoofing_scenario(symbol, base_price)
            print(f"   Generated {len(market_data)} data points")

            # Run detection
            signals = await crypto_spoofing_detector.detect(market_data)
            all_signals.extend(signals)

            if signals:
                for signal in signals:
                    print(f"   🚨 SPOOFING DETECTED!")
                    print(f"      Confidence: {signal.confidence:.1%}")
                    print(f"      Description: {signal.description}")
                    print(f"      Estimated Profit: ${signal.estimated_profit:,.0f}")

                    # Show additional crypto-specific data
                    if hasattr(signal, 'additional_data') and signal.additional_data:
                        extra = signal.additional_data
                        print(f"      Volume Spike: {extra.get('volume_spike_ratio', 0):.1f}x")
                        print(f"      Price Impact: {extra.get('price_impact_bps', 0):.0f} bps")
                        print(f"      Wash Trading Score: {extra.get('wash_trading_score', 0):.1f}")
            else:
                print("   ✅ No manipulation detected")

        # Summary
        print(f"\n📈 Detection Summary")
        print("=" * 60)
        print(f"Total signals detected: {len(all_signals)}")

        if all_signals:
            avg_confidence = np.mean([s.confidence for s in all_signals])
            total_profit = sum(s.estimated_profit for s in all_signals)

            print(f"Average confidence: {avg_confidence:.1%}")
            print(f"Total estimated profit: ${total_profit:,.0f}")

            # Show confidence distribution
            high_conf = len([s for s in all_signals if s.confidence >= 0.8])
            medium_conf = len([s for s in all_signals if 0.6 <= s.confidence < 0.8])
            low_conf = len([s for s in all_signals if s.confidence < 0.6])

            print(f"\nConfidence Distribution:")
            print(f"  High (≥80%): {high_conf} signals")
            print(f"  Medium (60-80%): {medium_conf} signals")
            print(f"  Low (<60%): {low_conf} signals")

        # Show detector statistics
        stats = crypto_spoofing_detector.get_crypto_statistics()
        print(f"\n⚡ Performance Metrics")
        print("-" * 30)
        print(f"Detection runs: {stats['execution_count']}")
        print(f"Signals generated: {stats['signals_generated']}")
        print(f"Avg processing time: {stats.get('avg_processing_time_ms', 0):.1f}ms")
        print(f"Volume threshold: ${stats['volume_threshold_usdt']:,.0f} USDT")
        print(f"Time window: {stats['time_window_seconds']}s")

    except Exception as e:
        print(f"❌ Demo error: {str(e)}")
        import traceback
        traceback.print_exc()

def show_crypto_advantages():
    """
    Show advantages of crypto adaptation
    """
    print("\n🎯 Crypto vs NSE Advantages")
    print("=" * 60)

    advantages = [
        ("Real-time data", "Binance WebSocket streams", "Delayed NSE data"),
        ("Market hours", "24/7 operation", "6 hours/day"),
        ("Latency", "<10ms data feeds", "800ms+ pipeline"),
        ("API access", "Professional APIs", "Limited/unreliable"),
        ("Market coverage", "100+ crypto pairs", "3 index options"),
        ("Liquidity", "$50B+ daily volume", "Limited options volume"),
        ("Volatility", "Higher profit potential", "Lower volatility"),
        ("Regulation", "No data restrictions", "Regulatory barriers")
    ]

    print(f"{'Aspect':<15} {'Crypto (Binance)':<25} {'NSE Options':<20}")
    print("-" * 60)

    for aspect, crypto_val, nse_val in advantages:
        print(f"{aspect:<15} {crypto_val:<25} {nse_val:<20}")

    print(f"\n💰 Expected Performance Improvements:")
    print(f"   • 8x faster signal generation (100ms vs 800ms)")
    print(f"   • 4x more trading time (24/7 vs 6hrs/day)")
    print(f"   • 7x more opportunities (100+ pairs vs 3)")
    print(f"   • Professional-grade data quality")

if __name__ == "__main__":
    print("🔥 Crypto Manipulation Detection System")
    print("Transforming NSE system for crypto markets")
    print()

    # Show advantages first
    show_crypto_advantages()

    # Run the demo
    asyncio.run(demo_crypto_detection())