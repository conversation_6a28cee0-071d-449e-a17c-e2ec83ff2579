"""
Binance Data Collector - Real-time crypto market data collection
Replaces NSE collector with high-frequency Binance WebSocket streams
"""
import asyncio
import json
import websockets
import aiohttp
from typing import List, Dict, Any, Optional, Callable
from datetime import datetime, timedelta
import logging
from dataclasses import dataclass
from enum import Enum
import numpy as np

from utils.latency_tracker import latency_tracker
from utils.enhanced_logging import enhanced_logger
from utils.cache import cache_manager

logger = logging.getLogger(__name__)

class StreamType(Enum):
    """WebSocket stream types"""
    ORDERBOOK = "depth"
    TRADES = "trade"
    KLINES = "kline"
    TICKER = "ticker"
    FUTURES = "futures"

@dataclass
class BinanceConfig:
    """Binance API configuration"""
    base_url: str = "https://api.binance.com"
    ws_url: str = "wss://stream.binance.com:9443/ws"
    futures_ws_url: str = "wss://fstream.binance.com/ws"

    # Rate limits
    requests_per_minute: int = 1200
    weight_per_minute: int = 6000

    # WebSocket settings
    ping_interval: int = 20
    ping_timeout: int = 10
    close_timeout: int = 10

@dataclass
class CryptoMarketData:
    """Crypto market data structure"""
    symbol: str
    timestamp: datetime
    price: float
    volume: float
    bid_price: float
    ask_price: float
    bid_qty: float
    ask_qty: float

    # Additional crypto-specific fields
    price_change_24h: float
    volume_24h: float
    high_24h: float
    low_24h: float
    trades_count: int

@dataclass
class OrderBookData:
    """Order book data structure"""
    symbol: str
    timestamp: datetime
    bids: List[List[float]]  # [price, quantity]
    asks: List[List[float]]  # [price, quantity]
    last_update_id: int

@dataclass
class TradeData:
    """Individual trade data"""
    symbol: str
    timestamp: datetime
    price: float
    quantity: float
    is_buyer_maker: bool
    trade_id: int

class BinanceDataCollector:
    """
    High-frequency crypto data collector using Binance WebSocket streams
    Designed for <10ms latency manipulation detection
    """

    def __init__(self, config: BinanceConfig = None):
        self.config = config or BinanceConfig()

        # WebSocket connections
        self.ws_connections: Dict[str, websockets.WebSocketServerProtocol] = {}
        self.stream_callbacks: Dict[str, Callable] = {}

        # Data buffers for real-time processing
        self.orderbook_buffer: Dict[str, List[OrderBookData]] = {}
        self.trade_buffer: Dict[str, List[TradeData]] = {}
        self.kline_buffer: Dict[str, List[Dict]] = {}

        # Connection management
        self.session: Optional[aiohttp.ClientSession] = None
        self.running = False

        # Statistics
        self.stats = {
            "messages_received": 0,
            "reconnections": 0,
            "last_message_time": None,
            "active_streams": 0,
            "latency_ms": 0.0
        }

        # Symbols to monitor
        self.symbols = [
            "BTCUSDT", "ETHUSDT", "BNBUSDT", "SOLUSDT",
            "ADAUSDT", "DOTUSDT", "AVAXUSDT", "LINKUSDT",
            "UNIUSDT", "MATICUSDT"
        ]

    async def initialize(self):
        """Initialize Binance data collector"""
        try:
            enhanced_logger.logger.info("🚀 Initializing Binance data collector")

            # Create HTTP session
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=30),
                headers={
                    'User-Agent': 'CryptoManipulationDetector/1.0',
                    'Content-Type': 'application/json'
                }
            )

            # Test connectivity
            await self._test_connectivity()

            # Get exchange info
            exchange_info = await self._get_exchange_info()
            self._validate_symbols(exchange_info)

            enhanced_logger.logger.info("✅ Binance collector initialized successfully")

        except Exception as e:
            enhanced_logger.logger.error(f"❌ Failed to initialize Binance collector: {str(e)}")
            raise

    async def _test_connectivity(self):
        """Test Binance API connectivity"""
        try:
            async with self.session.get(f"{self.config.base_url}/api/v3/ping") as response:
                if response.status == 200:
                    enhanced_logger.logger.info("✅ Binance API connectivity test passed")
                else:
                    raise Exception(f"API connectivity test failed: {response.status}")
        except Exception as e:
            raise Exception(f"Failed to connect to Binance API: {str(e)}")

    async def _get_exchange_info(self) -> Dict[str, Any]:
        """Get exchange information"""
        try:
            async with self.session.get(f"{self.config.base_url}/api/v3/exchangeInfo") as response:
                if response.status == 200:
                    return await response.json()
                else:
                    raise Exception(f"Failed to get exchange info: {response.status}")
        except Exception as e:
            raise Exception(f"Error getting exchange info: {str(e)}")

    def _validate_symbols(self, exchange_info: Dict[str, Any]):
        """Validate that symbols are tradeable"""
        available_symbols = {s['symbol'] for s in exchange_info['symbols'] if s['status'] == 'TRADING'}

        invalid_symbols = [s for s in self.symbols if s not in available_symbols]
        if invalid_symbols:
            enhanced_logger.logger.warning(f"⚠️ Invalid symbols removed: {invalid_symbols}")
            self.symbols = [s for s in self.symbols if s in available_symbols]

        enhanced_logger.logger.info(f"📊 Monitoring {len(self.symbols)} symbols: {self.symbols}")

    async def start_streams(self):
        """Start all WebSocket streams"""
        try:
            self.running = True
            enhanced_logger.logger.info("🔄 Starting Binance WebSocket streams")

            # Start individual streams
            tasks = []

            # Order book streams (for spoofing detection)
            for symbol in self.symbols:
                task = asyncio.create_task(
                    self._start_orderbook_stream(symbol.lower())
                )
                tasks.append(task)

            # Trade streams (for wash trading detection)
            for symbol in self.symbols:
                task = asyncio.create_task(
                    self._start_trade_stream(symbol.lower())
                )
                tasks.append(task)

            # Ticker streams (for pump/dump detection)
            task = asyncio.create_task(
                self._start_ticker_stream()
            )
            tasks.append(task)

            # Wait for all streams to start
            await asyncio.gather(*tasks, return_exceptions=True)

            enhanced_logger.logger.info(f"✅ Started {len(tasks)} WebSocket streams")

        except Exception as e:
            enhanced_logger.logger.error(f"❌ Error starting streams: {str(e)}")
            raise

    async def _start_orderbook_stream(self, symbol: str):
        """Start order book depth stream for a symbol"""
        stream_name = f"{symbol}@depth20@100ms"
        uri = f"{self.config.ws_url}/{stream_name}"

        while self.running:
            try:
                async with websockets.connect(
                    uri,
                    ping_interval=self.config.ping_interval,
                    ping_timeout=self.config.ping_timeout,
                    close_timeout=self.config.close_timeout
                ) as websocket:

                    enhanced_logger.logger.info(f"📡 Connected to orderbook stream: {symbol}")
                    self.stats["active_streams"] += 1

                    async for message in websocket:
                        if not self.running:
                            break

                        await self._process_orderbook_message(message, symbol)

            except websockets.exceptions.ConnectionClosed:
                enhanced_logger.logger.warning(f"🔌 Orderbook stream disconnected: {symbol}")
                self.stats["reconnections"] += 1
                await asyncio.sleep(5)  # Wait before reconnecting

            except Exception as e:
                enhanced_logger.logger.error(f"❌ Error in orderbook stream {symbol}: {str(e)}")
                await asyncio.sleep(10)

    async def _start_trade_stream(self, symbol: str):
        """Start individual trade stream for a symbol"""
        stream_name = f"{symbol}@trade"
        uri = f"{self.config.ws_url}/{stream_name}"

        while self.running:
            try:
                async with websockets.connect(
                    uri,
                    ping_interval=self.config.ping_interval,
                    ping_timeout=self.config.ping_timeout,
                    close_timeout=self.config.close_timeout
                ) as websocket:

                    enhanced_logger.logger.info(f"📡 Connected to trade stream: {symbol}")
                    self.stats["active_streams"] += 1

                    async for message in websocket:
                        if not self.running:
                            break

                        await self._process_trade_message(message, symbol)

            except websockets.exceptions.ConnectionClosed:
                enhanced_logger.logger.warning(f"🔌 Trade stream disconnected: {symbol}")
                self.stats["reconnections"] += 1
                await asyncio.sleep(5)

            except Exception as e:
                enhanced_logger.logger.error(f"❌ Error in trade stream {symbol}: {str(e)}")
                await asyncio.sleep(10)

    async def _start_ticker_stream(self):
        """Start 24hr ticker stream for all symbols"""
        symbols_lower = [s.lower() for s in self.symbols]
        stream_name = "/".join([f"{symbol}@ticker" for symbol in symbols_lower])
        uri = f"{self.config.ws_url}/{stream_name}"

        while self.running:
            try:
                async with websockets.connect(
                    uri,
                    ping_interval=self.config.ping_interval,
                    ping_timeout=self.config.ping_timeout,
                    close_timeout=self.config.close_timeout
                ) as websocket:

                    enhanced_logger.logger.info("📡 Connected to ticker stream")
                    self.stats["active_streams"] += 1

                    async for message in websocket:
                        if not self.running:
                            break

                        await self._process_ticker_message(message)

            except websockets.exceptions.ConnectionClosed:
                enhanced_logger.logger.warning("🔌 Ticker stream disconnected")
                self.stats["reconnections"] += 1
                await asyncio.sleep(5)

            except Exception as e:
                enhanced_logger.logger.error(f"❌ Error in ticker stream: {str(e)}")
                await asyncio.sleep(10)

    async def _process_orderbook_message(self, message: str, symbol: str):
        """Process order book depth message"""
        try:
            # Track message latency
            async with latency_tracker.measure_async('orderbook_processing', {'symbol': symbol}):
                data = json.loads(message)

                # Extract order book data
                orderbook = OrderBookData(
                    symbol=symbol.upper(),
                    timestamp=datetime.now(),
                    bids=[[float(bid[0]), float(bid[1])] for bid in data['bids']],
                    asks=[[float(ask[0]), float(ask[1])] for ask in data['asks']],
                    last_update_id=data['lastUpdateId']
                )

                # Add to buffer (keep last 100 updates)
                if symbol not in self.orderbook_buffer:
                    self.orderbook_buffer[symbol] = []

                self.orderbook_buffer[symbol].append(orderbook)
                if len(self.orderbook_buffer[symbol]) > 100:
                    self.orderbook_buffer[symbol].pop(0)

                # Update statistics
                self.stats["messages_received"] += 1
                self.stats["last_message_time"] = datetime.now()

                # Cache latest orderbook
                await cache_manager.set(
                    f"orderbook:{symbol}",
                    orderbook,
                    ttl=5  # 5 second TTL
                )

        except Exception as e:
            enhanced_logger.logger.error(f"❌ Error processing orderbook message: {str(e)}")

    async def _process_trade_message(self, message: str, symbol: str):
        """Process individual trade message"""
        try:
            async with latency_tracker.measure_async('trade_processing', {'symbol': symbol}):
                data = json.loads(message)

                # Extract trade data
                trade = TradeData(
                    symbol=symbol.upper(),
                    timestamp=datetime.fromtimestamp(data['T'] / 1000),
                    price=float(data['p']),
                    quantity=float(data['q']),
                    is_buyer_maker=data['m'],
                    trade_id=data['t']
                )

                # Add to buffer (keep last 1000 trades)
                if symbol not in self.trade_buffer:
                    self.trade_buffer[symbol] = []

                self.trade_buffer[symbol].append(trade)
                if len(self.trade_buffer[symbol]) > 1000:
                    self.trade_buffer[symbol].pop(0)

                # Update statistics
                self.stats["messages_received"] += 1
                self.stats["last_message_time"] = datetime.now()

        except Exception as e:
            enhanced_logger.logger.error(f"❌ Error processing trade message: {str(e)}")

    async def _process_ticker_message(self, message: str):
        """Process 24hr ticker message"""
        try:
            async with latency_tracker.measure_async('ticker_processing'):
                data = json.loads(message)

                # Handle both single ticker and array of tickers
                tickers = data if isinstance(data, list) else [data]

                for ticker_data in tickers:
                    symbol = ticker_data['s']

                    # Create crypto market data
                    market_data = CryptoMarketData(
                        symbol=symbol,
                        timestamp=datetime.fromtimestamp(ticker_data['C'] / 1000),
                        price=float(ticker_data['c']),
                        volume=float(ticker_data['v']),
                        bid_price=float(ticker_data['b']),
                        ask_price=float(ticker_data['a']),
                        bid_qty=float(ticker_data['B']),
                        ask_qty=float(ticker_data['A']),
                        price_change_24h=float(ticker_data['P']),
                        volume_24h=float(ticker_data['q']),
                        high_24h=float(ticker_data['h']),
                        low_24h=float(ticker_data['l']),
                        trades_count=int(ticker_data['c'])
                    )

                    # Cache market data
                    await cache_manager.set(
                        f"market_data:{symbol}",
                        market_data,
                        ttl=10  # 10 second TTL
                    )

                # Update statistics
                self.stats["messages_received"] += len(tickers)
                self.stats["last_message_time"] = datetime.now()

        except Exception as e:
            enhanced_logger.logger.error(f"❌ Error processing ticker message: {str(e)}")

    async def get_market_data(self, symbols: List[str] = None) -> List[CryptoMarketData]:
        """Get current market data for symbols"""
        if symbols is None:
            symbols = self.symbols

        market_data = []

        for symbol in symbols:
            try:
                # Get from cache first
                cached_data = await cache_manager.get(f"market_data:{symbol}")
                if cached_data:
                    market_data.append(cached_data)
                else:
                    # Fallback to REST API if no cached data
                    rest_data = await self._get_ticker_rest(symbol)
                    if rest_data:
                        market_data.append(rest_data)

            except Exception as e:
                enhanced_logger.logger.error(f"❌ Error getting market data for {symbol}: {str(e)}")

        return market_data

    async def _get_ticker_rest(self, symbol: str) -> Optional[CryptoMarketData]:
        """Get ticker data via REST API as fallback"""
        try:
            url = f"{self.config.base_url}/api/v3/ticker/24hr"
            params = {"symbol": symbol}

            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()

                    return CryptoMarketData(
                        symbol=symbol,
                        timestamp=datetime.fromtimestamp(data['closeTime'] / 1000),
                        price=float(data['lastPrice']),
                        volume=float(data['volume']),
                        bid_price=float(data['bidPrice']),
                        ask_price=float(data['askPrice']),
                        bid_qty=float(data['bidQty']),
                        ask_qty=float(data['askQty']),
                        price_change_24h=float(data['priceChangePercent']),
                        volume_24h=float(data['quoteVolume']),
                        high_24h=float(data['highPrice']),
                        low_24h=float(data['lowPrice']),
                        trades_count=int(data['count'])
                    )

        except Exception as e:
            enhanced_logger.logger.error(f"❌ Error getting REST ticker for {symbol}: {str(e)}")
            return None

    def get_statistics(self) -> Dict[str, Any]:
        """Get collector statistics"""
        return {
            **self.stats,
            "symbols_monitored": len(self.symbols),
            "orderbook_buffers": len(self.orderbook_buffer),
            "trade_buffers": len(self.trade_buffer),
            "running": self.running
        }

    async def shutdown(self):
        """Shutdown the collector"""
        enhanced_logger.logger.info("🛑 Shutting down Binance data collector")

        self.running = False

        # Close WebSocket connections
        for ws in self.ws_connections.values():
            await ws.close()

        # Close HTTP session
        if self.session:
            await self.session.close()

        enhanced_logger.logger.info("✅ Binance collector shutdown complete")

# Global instance
binance_collector = BinanceDataCollector()