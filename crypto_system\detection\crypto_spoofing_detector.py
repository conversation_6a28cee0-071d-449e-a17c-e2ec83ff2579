"""
Crypto Spoofing Detector - High-frequency manipulation detection for crypto markets
Adapted from the existing vectorized detector for crypto market characteristics
"""
import asyncio
import numpy as np
from numba import jit
from typing import List, Dict, Any, Tuple
from datetime import datetime, timedelta
import logging

from detection.base_detector import BaseDetector
from models.data_models import ManipulationSignal, PatternType, ConfidenceLevel
from utils.enhanced_logging import enhanced_logger
from utils.latency_tracker import latency_tracker

logger = logging.getLogger(__name__)

@jit(nopython=True)
def detect_crypto_spoofing_vectorized(
    bid_qtys: np.ndarray,
    ask_qtys: np.ndarray,
    prices: np.ndarray,
    volumes: np.ndarray,
    timestamps: np.ndarray,
    volume_threshold_usdt: float = 50000.0,  # $50k USDT threshold
    time_window_seconds: float = 10.0,       # 10 second window (faster than stocks)
    price_impact_threshold: float = 0.005    # 0.5% price impact (crypto volatility)
) -> np.ndarray:
    """
    Vectorized crypto spoofing detection optimized for high-frequency crypto markets

    Crypto-specific adaptations:
    - Higher volume thresholds (USDT denominated)
    - Shorter time windows (10s vs 30s for stocks)
    - Higher price impact tolerance (0.5% vs 0.01% for stocks)
    - 24/7 operation (no market hours)

    Args:
        bid_qtys: Array of bid quantities (in base currency)
        ask_qtys: Array of ask quantities (in base currency)
        prices: Array of prices (in USDT)
        volumes: Array of trade volumes (in USDT)
        timestamps: Array of timestamps (in seconds)
        volume_threshold_usdt: Minimum USDT volume to consider spoofing
        time_window_seconds: Maximum time window for spoofing pattern
        price_impact_threshold: Minimum price impact to consider manipulation

    Returns:
        Array of indices where crypto spoofing patterns were detected
    """
    n = len(bid_qtys)
    if n < 3:
        return np.array([], dtype=np.int64)

    # Convert quantities to USDT value for threshold comparison
    bid_values_usdt = bid_qtys * prices
    ask_values_usdt = ask_qtys * prices

    # Vectorized calculations - O(n) complexity
    bid_changes = np.diff(bid_values_usdt)
    ask_changes = np.diff(ask_values_usdt)
    price_changes = np.diff(prices)
    time_diffs = np.diff(timestamps)

    # Crypto spoofing pattern detection:
    # 1. Large USDT value spike followed by reversal
    # 2. Within crypto-appropriate time window (10s)
    # 3. Minimal price impact relative to crypto volatility (0.5%)
    # 4. High volume activity (wash trading indicator)

    # Bid spoofing pattern
    bid_spoof_pattern = (
        (np.abs(bid_changes[:-1]) > volume_threshold_usdt) &  # Large initial change in USDT
        (np.abs(bid_changes[1:]) > volume_threshold_usdt) &   # Large reversal in USDT
        (np.sign(bid_changes[:-1]) != np.sign(bid_changes[1:])) &  # Opposite direction
        (time_diffs[1:] <= time_window_seconds) &             # Within crypto time window
        (np.abs(price_changes[1:] / prices[1:-1]) < price_impact_threshold)  # Minimal price impact
    )

    # Ask spoofing pattern
    ask_spoof_pattern = (
        (np.abs(ask_changes[:-1]) > volume_threshold_usdt) &
        (np.abs(ask_changes[1:]) > volume_threshold_usdt) &
        (np.sign(ask_changes[:-1]) != np.sign(ask_changes[1:])) &
        (time_diffs[1:] <= time_window_seconds) &
        (np.abs(price_changes[1:] / prices[1:-1]) < price_impact_threshold)
    )

    # Combine patterns
    spoof_pattern = bid_spoof_pattern | ask_spoof_pattern

    return np.where(spoof_pattern)[0] + 1  # Return indices (offset by 1)

@jit(nopython=True)
def calculate_crypto_market_evidence(
    bid_qtys: np.ndarray,
    ask_qtys: np.ndarray,
    prices: np.ndarray,
    volumes: np.ndarray,
    timestamps: np.ndarray,
    idx: int
) -> Tuple[float, float, float, float, float, float]:
    """
    Calculate crypto market microstructure evidence at specific index

    Returns:
        Tuple of (volume_spike_ratio, price_impact_bps, spread_bps,
                 order_imbalance, time_window_seconds, wash_trading_score)
    """
    if idx <= 2 or idx >= len(prices) - 2:
        return 0.0, 0.0, 0.0, 0.0, 0.0, 0.0

    # Volume spike analysis (crypto-specific)
    current_volume = volumes[idx]
    avg_volume = np.mean(volumes[max(0, idx-20):idx])  # 20-period average
    volume_spike_ratio = current_volume / avg_volume if avg_volume > 0 else 1.0

    # Price impact in basis points
    price_change = prices[idx] - prices[idx-1]
    price_impact_bps = (price_change / prices[idx-1]) * 10000 if prices[idx-1] > 0 else 0.0

    # Bid-ask spread in basis points
    current_bid = bid_qtys[idx] * prices[idx]
    current_ask = ask_qtys[idx] * prices[idx]
    if current_bid > 0 and current_ask > 0:
        spread_bps = ((current_ask - current_bid) / ((current_ask + current_bid) / 2)) * 10000
    else:
        spread_bps = 0.0

    # Order book imbalance
    total_liquidity = current_bid + current_ask
    order_imbalance = (current_bid - current_ask) / total_liquidity if total_liquidity > 0 else 0.0

    # Time window for pattern
    time_window_seconds = timestamps[idx] - timestamps[max(0, idx-5)]

    # Wash trading score (high volume with low price impact)
    wash_trading_score = volume_spike_ratio / (abs(price_impact_bps) + 1) if price_impact_bps != 0 else volume_spike_ratio

    return (
        float(volume_spike_ratio),
        float(price_impact_bps),
        float(spread_bps),
        float(order_imbalance),
        float(time_window_seconds),
        float(wash_trading_score)
    )

class CryptoSpoofingDetector(BaseDetector):
    """
    High-frequency crypto spoofing detector
    Optimized for crypto market characteristics and Binance data streams
    """

    def __init__(self):
        super().__init__(
            name="crypto_spoofing_detector",
            description="High-frequency spoofing detection for crypto markets"
        )

        # Crypto-specific thresholds
        self.volume_threshold_usdt = 50000.0  # $50k USDT minimum
        self.time_window_seconds = 10.0       # 10 second window
        self.price_impact_threshold = 0.005   # 0.5% price impact
        self.min_confidence_threshold = 0.6   # 60% minimum confidence

        # Performance tracking
        self.detection_count = 0
        self.total_processing_time = 0.0

        enhanced_logger.logger.info("🔍 Crypto spoofing detector initialized")
        enhanced_logger.logger.info(f"   Volume threshold: ${self.volume_threshold_usdt:,.0f} USDT")
        enhanced_logger.logger.info(f"   Time window: {self.time_window_seconds}s")
        enhanced_logger.logger.info(f"   Price impact threshold: {self.price_impact_threshold:.1%}")

    async def detect(self, data: List[Any]) -> List[ManipulationSignal]:
        """
        Main detection method for crypto market data
        """
        if not data:
            return []

        import time
        start_time = time.time()

        signals = []

        try:
            # Convert generic data to crypto market data format
            crypto_data = []
            for item in data:
                if hasattr(item, 'symbol') and hasattr(item, 'price'):
                    crypto_data.append(item)

            if not crypto_data:
                return []

            # Group data by symbol for analysis
            symbol_groups = {}
            for market_data in crypto_data:
                symbol = market_data.symbol
                if symbol not in symbol_groups:
                    symbol_groups[symbol] = []
                symbol_groups[symbol].append(market_data)

            # Analyze each symbol separately
            for symbol, symbol_data in symbol_groups.items():
                if len(symbol_data) < 5:  # Need minimum data points
                    continue

                # Sort by timestamp
                symbol_data.sort(key=lambda x: x.timestamp)

                # Convert to numpy arrays for vectorized processing
                bid_qtys = np.array([getattr(d, 'bid_qty', 0.0) for d in symbol_data], dtype=np.float64)
                ask_qtys = np.array([getattr(d, 'ask_qty', 0.0) for d in symbol_data], dtype=np.float64)
                prices = np.array([d.price for d in symbol_data], dtype=np.float64)
                volumes = np.array([getattr(d, 'volume', 0.0) * d.price for d in symbol_data], dtype=np.float64)  # USDT volume
                timestamps = np.array([d.timestamp.timestamp() for d in symbol_data], dtype=np.float64)

                # Vectorized spoofing detection
                spoof_indices = detect_crypto_spoofing_vectorized(
                    bid_qtys, ask_qtys, prices, volumes, timestamps,
                    self.volume_threshold_usdt, self.time_window_seconds, self.price_impact_threshold
                )

                # Create signals for detected patterns
                for idx in spoof_indices:
                    signal = await self._create_crypto_signal(
                        symbol_data, idx, symbol, bid_qtys, ask_qtys, prices, volumes, timestamps
                    )
                    if signal:
                        signals.append(signal)

            # Update performance metrics
            processing_time = time.time() - start_time
            self.detection_count += 1
            self.total_processing_time += processing_time

            avg_time = self.total_processing_time / self.detection_count
            enhanced_logger.logger.info(
                f"🔍 Crypto spoofing detection completed",
                signals_found=len(signals),
                processing_time_ms=processing_time * 1000,
                avg_processing_time_ms=avg_time * 1000,
                symbols_analyzed=len(symbol_groups)
            )

        except Exception as e:
            enhanced_logger.logger.error(f"❌ Error in crypto spoofing detection: {str(e)}")

        return signals

    async def _create_crypto_signal(
        self,
        symbol_data: List[Any],
        idx: int,
        symbol: str,
        bid_qtys: np.ndarray,
        ask_qtys: np.ndarray,
        prices: np.ndarray,
        volumes: np.ndarray,
        timestamps: np.ndarray
    ) -> ManipulationSignal:
        """Create manipulation signal from detected crypto spoofing pattern"""

        try:
            # Calculate market evidence using vectorized operations
            evidence = calculate_crypto_market_evidence(
                bid_qtys, ask_qtys, prices, volumes, timestamps, idx
            )

            volume_spike_ratio, price_impact_bps, spread_bps, order_imbalance, time_window, wash_trading_score = evidence

            # Calculate confidence based on crypto market evidence
            confidence_factors = {
                'volume_spike': min(volume_spike_ratio / 5.0, 1.0),  # Normalize to 5x spike
                'price_impact': 1.0 - min(abs(price_impact_bps) / 500.0, 1.0),  # Lower impact = higher confidence
                'spread_tightness': 1.0 - min(spread_bps / 1000.0, 1.0),  # Tighter spread = higher confidence
                'order_imbalance': min(abs(order_imbalance), 1.0),  # Higher imbalance = higher confidence
                'wash_trading': min(wash_trading_score / 10.0, 1.0)  # Higher wash score = higher confidence
            }

            # Weighted confidence calculation
            confidence = (
                confidence_factors['volume_spike'] * 0.3 +
                confidence_factors['price_impact'] * 0.25 +
                confidence_factors['spread_tightness'] * 0.2 +
                confidence_factors['order_imbalance'] * 0.15 +
                confidence_factors['wash_trading'] * 0.1
            )

            # Only create signal if confidence meets threshold
            if confidence < self.min_confidence_threshold:
                return None

            # Estimate profit potential (crypto-specific)
            current_price = prices[idx]
            volume_usdt = volumes[idx]

            # Profit estimation based on volume and price impact
            estimated_profit_usdt = volume_usdt * 0.001 * confidence  # 0.1% of volume * confidence

            # Determine confidence level
            if confidence >= 0.85:
                confidence_level = ConfidenceLevel.HIGH
            elif confidence >= 0.7:
                confidence_level = ConfidenceLevel.MEDIUM
            else:
                confidence_level = ConfidenceLevel.LOW

            # Create description
            description = (
                f"Crypto spoofing detected in {symbol}: "
                f"{volume_spike_ratio:.1f}x volume spike, "
                f"{abs(price_impact_bps):.0f}bps price impact, "
                f"{wash_trading_score:.1f} wash trading score"
            )

            # Create manipulation signal
            signal = ManipulationSignal(
                pattern_type=PatternType.SPOOFING,
                timestamp=symbol_data[idx].timestamp,
                symbols_affected=[symbol],
                description=description,

                # Market microstructure evidence (crypto-adapted)
                oi_change_pct_1min=0.0,  # Not applicable for spot crypto
                iv_spike_z_score=0.0,    # Not applicable for spot crypto
                bid_ask_spread_pct=spread_bps / 100.0,
                traded_volume_vs_avg=volume_spike_ratio,
                order_book_imbalance_ratio=order_imbalance,

                # Confidence and profit
                confidence=confidence,
                confidence_level=confidence_level,
                estimated_profit=estimated_profit_usdt,

                # Additional metadata
                market_impact=f"Volume: ${volume_usdt:,.0f} USDT, Impact: {price_impact_bps:.0f}bps",
                detection_algorithm="crypto_spoofing_vectorized",

                # Crypto-specific fields
                additional_data={
                    "volume_spike_ratio": volume_spike_ratio,
                    "price_impact_bps": price_impact_bps,
                    "spread_bps": spread_bps,
                    "wash_trading_score": wash_trading_score,
                    "time_window_seconds": time_window,
                    "volume_usdt": volume_usdt,
                    "price_usdt": current_price
                }
            )

            enhanced_logger.logger.warning(
                f"🚨 CRYPTO SPOOFING DETECTED: {symbol}",
                confidence=f"{confidence:.1%}",
                volume_spike=f"{volume_spike_ratio:.1f}x",
                price_impact=f"{price_impact_bps:.0f}bps",
                estimated_profit=f"${estimated_profit_usdt:,.0f}"
            )

            return signal

        except Exception as e:
            enhanced_logger.logger.error(f"❌ Error creating crypto signal: {str(e)}")
            return None

    def get_required_data_window(self) -> int:
        """Return minimum number of data points required for crypto detection"""
        return 5  # Need at least 5 points for crypto analysis

    def get_crypto_statistics(self) -> Dict[str, Any]:
        """Get crypto-specific detector statistics"""
        base_stats = self.get_statistics()

        crypto_stats = {
            **base_stats,
            "volume_threshold_usdt": self.volume_threshold_usdt,
            "time_window_seconds": self.time_window_seconds,
            "price_impact_threshold": self.price_impact_threshold,
            "min_confidence_threshold": self.min_confidence_threshold,
            "avg_processing_time_ms": (self.total_processing_time / self.detection_count * 1000) if self.detection_count > 0 else 0
        }

        return crypto_stats

# Global instance for easy import
crypto_spoofing_detector = CryptoSpoofingDetector()